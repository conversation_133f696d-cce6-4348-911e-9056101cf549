# Hướng dẫn thiết lập Firebase Authentication cho BloodPlus

## ✅ Đã hoàn thành

### 1. Cấu hình Dependencies
- ✅ Thêm Firebase Core và Firebase Auth vào `pubspec.yaml`
- ✅ Thêm Pinput cho OTP input
- ✅ Cập nhật minSdkVersion từ 21 lên 23 (y<PERSON><PERSON> c<PERSON><PERSON> của Firebase Auth)
- ✅ Thêm Google Services plugin vào Android build

### 2. Cấu hình Android
- ✅ Cập nhật `android/app/build.gradle.kts` với Google Services
- ✅ Cập nhật `android/build.gradle.kts` với Google Services plugin
- ✅ Thêm quyền SMS cho Firebase Auth trong AndroidManifest.xml
- ✅ File `google-services.json` đã có sẵn

### 3. Code Implementation
- ✅ Tạo `PhoneAuthService` cho Firebase phone authentication
- ✅ Tạo `RegistrationModel` cho dữ liệu đăng ký
- ✅ Mở rộng `AuthService` với methods mới:
  - `checkInformation()` - <PERSON><PERSON><PERSON> tra email/phone đã tồn tại
  - `createAccount()` - Tạo tài khoản mới
- ✅ Tạo 3 screens mới:
  - `CheckInformationScreen` - Nhập email và số điện thoại
  - `PhoneVerificationScreen` - Xác thực OTP
  - `RegistrationScreen` - Form đăng ký đầy đủ
- ✅ Cập nhật `LoginScreen` để link đến registration flow

## 🔧 Cần thiết lập thêm

### 1. Firebase Console Setup
1. Truy cập https://console.firebase.google.com/
2. Chọn project `bloodplus-6be56`
3. Vào **Authentication** > **Sign-in method**
4. Kích hoạt **Phone** provider
5. Thêm số điện thoại test cho development (tùy chọn)

### 2. Android SHA-1 Fingerprint
```bash
cd android
./gradlew signingReport
```
Copy SHA-1 fingerprint và thêm vào Firebase Console:
- **Project Settings** > **Your apps** > **Android app**
- Thêm SHA certificate fingerprints

### 3. Backend API Endpoints
Đảm bảo backend có các endpoints:
- `POST /auth/check-information` - Kiểm tra email/phone
- `POST /auth/verify-phonenumber` - Xác thực Firebase ID token
- `POST /auth/create-account` - Tạo tài khoản (multipart form data)

## 🚀 Test Flow

### 1. Chạy ứng dụng
```bash
flutter run
```

### 2. Test Registration Flow
1. **Login Screen** → Click "Đăng ký"
2. **Check Information Screen** → Nhập email + số điện thoại
3. **Phone Verification Screen** → Nhập OTP từ SMS
4. **Registration Screen** → Điền form đăng ký đầy đủ
5. **Home Screen** → Đăng ký thành công

### 3. Lưu ý khi test
- Cần số điện thoại thật để nhận SMS OTP
- Firebase có giới hạn SMS miễn phí
- reCAPTCHA có thể xuất hiện trên emulator
- Kiểm tra console logs để debug

## 📱 Tính năng đã implement

### Phone Authentication
- Gửi OTP qua Firebase Auth
- Xác thực OTP với auto-detection
- Resend OTP với countdown timer
- Error handling cho các trường hợp lỗi

### Registration Form
- Upload ảnh đại diện
- Validation đầy đủ cho tất cả fields
- Dropdown cho giới tính và nhóm máu
- Date picker cho ngày sinh
- Password confirmation

### API Integration
- Kiểm tra email/phone trùng lặp
- Gửi Firebase ID token lên backend
- Tạo tài khoản với multipart data
- Auto login sau khi đăng ký thành công

## 🐛 Troubleshooting

### Build Errors
- Nếu gặp lỗi minSdkVersion: Đã fix bằng cách set minSdk = 23
- Nếu gặp lỗi Google Services: Đảm bảo google-services.json đúng vị trí

### Firebase Auth Errors
- `invalid-phone-number`: Kiểm tra format số điện thoại (+84...)
- `too-many-requests`: Đợi một lúc rồi thử lại
- `quota-exceeded`: Đã hết quota SMS miễn phí

### Network Errors
- Kiểm tra internet connection
- Kiểm tra backend API endpoints
- Xem console logs để debug

## 📋 Next Steps

1. Test trên thiết bị thật với số điện thoại thật
2. Thiết lập Firebase Console đầy đủ
3. Cấu hình backend APIs
4. Test end-to-end flow
5. Handle edge cases và error scenarios

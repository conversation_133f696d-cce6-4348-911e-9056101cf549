class RegistrationModel {
  final String userName;
  final String password;
  final String verifyPassword;
  final String email;
  final String phoneNumber;
  final String bloodType;
  final String name;
  final String dateOfBirth;
  final String address;
  final String job;
  final int gender;
  final String passportNumber;
  final String? userImage;
  final String? idToken;

  RegistrationModel({
    required this.userName,
    required this.password,
    required this.verifyPassword,
    required this.email,
    required this.phoneNumber,
    required this.bloodType,
    required this.name,
    required this.dateOfBirth,
    required this.address,
    required this.job,
    required this.gender,
    required this.passportNumber,
    this.userImage,
    this.idToken,
  });

  // <PERSON><PERSON><PERSON><PERSON> sang JSON để gửi API
  Map<String, dynamic> toJson() {
    return {
      'UserName': userName,
      'Password': password,
      'VerifyPassword': verifyPassword,
      'Email': email,
      'PhoneNumber': phoneNumber,
      'BloodType': bloodType,
      'Name': name,
      'DateOfBirth': dateOfBirth,
      'Address': address,
      'Job': job,
      'Gender': gender,
      'PassportNumber': passportNumber,
      'UserImage': userImage,
      'IdToken': idToken,
    };
  }

  // Tạo từ JSON
  factory RegistrationModel.fromJson(Map<String, dynamic> json) {
    return RegistrationModel(
      userName: json['UserName'] ?? json['userName'] ?? '',
      password: json['Password'] ?? json['password'] ?? '',
      verifyPassword: json['VerifyPassword'] ?? json['verifyPassword'] ?? '',
      email: json['Email'] ?? json['email'] ?? '',
      phoneNumber: json['PhoneNumber'] ?? json['phoneNumber'] ?? '',
      bloodType: json['BloodType'] ?? json['bloodType'] ?? '',
      name: json['Name'] ?? json['name'] ?? '',
      dateOfBirth: json['DateOfBirth'] ?? json['dateOfBirth'] ?? '',
      address: json['Address'] ?? json['address'] ?? '',
      job: json['Job'] ?? json['job'] ?? '',
      gender: json['Gender'] ?? json['gender'] ?? 0,
      passportNumber: json['PassportNumber'] ?? json['passportNumber'] ?? '',
      userImage: json['UserImage'] ?? json['userImage'],
      idToken: json['IdToken'] ?? json['idToken'],
    );
  }

  // Copy with method để tạo bản sao với một số thay đổi
  RegistrationModel copyWith({
    String? userName,
    String? password,
    String? verifyPassword,
    String? email,
    String? phoneNumber,
    String? bloodType,
    String? name,
    String? dateOfBirth,
    String? address,
    String? job,
    int? gender,
    String? passportNumber,
    String? userImage,
    String? idToken,
  }) {
    return RegistrationModel(
      userName: userName ?? this.userName,
      password: password ?? this.password,
      verifyPassword: verifyPassword ?? this.verifyPassword,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      bloodType: bloodType ?? this.bloodType,
      name: name ?? this.name,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      address: address ?? this.address,
      job: job ?? this.job,
      gender: gender ?? this.gender,
      passportNumber: passportNumber ?? this.passportNumber,
      userImage: userImage ?? this.userImage,
      idToken: idToken ?? this.idToken,
    );
  }

  // Validate dữ liệu
  Map<String, String?> validate() {
    Map<String, String?> errors = {};

    if (userName.isEmpty) {
      errors['userName'] = 'Tên đăng nhập không được để trống';
    } else if (userName.length < 3) {
      errors['userName'] = 'Tên đăng nhập phải có ít nhất 3 ký tự';
    }

    if (password.isEmpty) {
      errors['password'] = 'Mật khẩu không được để trống';
    } else if (password.length < 6) {
      errors['password'] = 'Mật khẩu phải có ít nhất 6 ký tự';
    }

    if (verifyPassword.isEmpty) {
      errors['verifyPassword'] = 'Xác nhận mật khẩu không được để trống';
    } else if (password != verifyPassword) {
      errors['verifyPassword'] = 'Mật khẩu xác nhận không khớp';
    }

    if (email.isEmpty) {
      errors['email'] = 'Email không được để trống';
    } else if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      errors['email'] = 'Email không hợp lệ';
    }

    if (phoneNumber.isEmpty) {
      errors['phoneNumber'] = 'Số điện thoại không được để trống';
    } else if (!RegExp(r'^[0-9]{10,11}$').hasMatch(phoneNumber)) {
      errors['phoneNumber'] = 'Số điện thoại không hợp lệ';
    }

    if (name.isEmpty) {
      errors['name'] = 'Họ tên không được để trống';
    }

    if (bloodType.isEmpty) {
      errors['bloodType'] = 'Nhóm máu không được để trống';
    }

    if (dateOfBirth.isEmpty) {
      errors['dateOfBirth'] = 'Ngày sinh không được để trống';
    }

    if (address.isEmpty) {
      errors['address'] = 'Địa chỉ không được để trống';
    }

    if (job.isEmpty) {
      errors['job'] = 'Nghề nghiệp không được để trống';
    }

    if (passportNumber.isEmpty) {
      errors['passportNumber'] = 'Số CCCD/Passport không được để trống';
    }

    return errors;
  }

  // Kiểm tra xem dữ liệu có hợp lệ không
  bool isValid() {
    return validate().isEmpty;
  }

  @override
  String toString() {
    return 'RegistrationModel(userName: $userName, email: $email, phoneNumber: $phoneNumber, name: $name)';
  }
}

// Enum cho giới tính
enum Gender {
  male(0, 'Nam'),
  female(1, 'Nữ'),
  other(2, 'Khác');

  const Gender(this.value, this.displayName);
  final int value;
  final String displayName;

  static Gender fromValue(int value) {
    return Gender.values.firstWhere(
      (gender) => gender.value == value,
      orElse: () => Gender.male,
    );
  }
}

// Enum cho nhóm máu
enum BloodType {
  aPositive('A+'),
  aNegative('A-'),
  bPositive('B+'),
  bNegative('B-'),
  abPositive('AB+'),
  abNegative('AB-'),
  oPositive('O+'),
  oNegative('O-');

  const BloodType(this.displayName);
  final String displayName;

  static BloodType? fromString(String value) {
    try {
      return BloodType.values.firstWhere(
        (bloodType) => bloodType.displayName == value,
      );
    } catch (e) {
      return null;
    }
  }
}

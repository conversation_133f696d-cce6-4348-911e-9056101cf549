import 'dart:convert';
import 'dart:io';
import 'package:bloodplus/core/config/api_config.dart';
import 'package:bloodplus/data/manager/app_state_notifier.dart';
import 'package:bloodplus/data/manager/user_manager.dart';
import 'package:bloodplus/data/services/user_service.dart';
import 'package:bloodplus/data/models/registration_model.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';

class AuthService {
  static final HttpClient _httpClient = HttpClient()
    ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
  static final IOClient _client = IOClient(_httpClient);
  final UserService _userService;
  final UserManager _userManager;
  final AppStateNotifier? _appStateNotifier;

  AuthService({
    AppStateNotifier? appStateNotifier,
    UserService? userService,
    UserManager? userManager,
  })  : _appStateNotifier = appStateNotifier,
        _userService = userService ?? UserService(),
        _userManager = userManager ?? UserManager();

  Future<Map<String, dynamic>> login(
      String username,
      String password,
      Map<String, String> localizedStrings,
      ) async {
    final url = Uri.parse(ApiConfig.getFullUrl(ApiConfig.authLogin));
    final body = jsonEncode({'username': username, 'password': password});

    try {
      print('=== LOGIN REQUEST ===');
      print('URL: $url');
      print('Body: $body');

      final response = await _client.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'accept': '*/*',
        },
        body: body,
      );

      print('=== LOGIN RESPONSE ===');
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final token = data['AccessToken']?.toString() ??
            data['accessToken']?.toString() ??
            data['token']?.toString();
        if (token == null) {
          throw Exception('Thiếu token trong phản hồi API');
        }

        final userId = _extractUserIdFromToken(token) ?? data['userId']?.toString();
        if (userId == null) {
          throw Exception('Không thể lấy userId từ token hoặc dữ liệu API');
        }

        _appStateNotifier?.resetState();
        await _userManager.saveUserToken(token);
        await _userManager.saveUserId(userId);
        final user = await _userService.getUserInfo(userId, token);
        await _userManager.saveUserInfo(userId, user);
        await _appStateNotifier?.fetchUserProfile(forceRefresh: true);

        return {
          'accessToken': token,
          'userId': userId,
        };
      } else {
        throw Exception(
          localizedStrings['login_failed']?.replaceAll('{statusCode}', response.statusCode.toString()) ??
              'Đăng nhập thất bại: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('Login error: $e');
      throw Exception(
        localizedStrings['connection_error']?.replaceAll('{error}', e.toString()) ??
            'Lỗi đăng nhập: $e',
      );
    }
  }

  Future<Map<String, dynamic>> loginWithGoogle(Map<String, String> localizedStrings) async {
    final googleSignIn = GoogleSignIn(
      scopes: ['email', 'profile'],
      serverClientId: '229734390839-3n21tokpf4sg0l6v7kur91dk78djvr9p.apps.googleusercontent.com',
    );

    try {
      final googleUser = await googleSignIn.signIn();
      if (googleUser == null) {
        return {'error': localizedStrings['google_login_cancelled'] ?? 'Đăng nhập Google bị hủy'};
      }

      final googleAuth = await googleUser.authentication;
      final idToken = googleAuth.idToken;
      if (idToken == null) {
        throw Exception(localizedStrings['google_token_error'] ?? 'Không thể lấy idToken từ Google');
      }

      final loginResult = await _attemptGoogleLogin(idToken, localizedStrings);
      if (loginResult['success'] == true) {
        return loginResult['data'];
      }

      if (loginResult['statusCode'] == 401) {
        final userInfo = {
          'name': googleUser.displayName ?? 'Google User',
          'email': googleUser.email,
          'userImage': googleUser.photoUrl,
        };

        if (!_isValidGoogleUserInfo(userInfo)) {
          throw Exception(localizedStrings['google_user_info_invalid'] ??
              'Thông tin tài khoản Google không đầy đủ');
        }

        await _attemptGoogleRegistration(userInfo, localizedStrings);
        final retryResult = await _attemptGoogleLogin(idToken, localizedStrings);

        if (retryResult['success'] == true) {
          return retryResult['data'];
        }

        return await _createFallbackGoogleLogin(googleUser, localizedStrings);
      }

      throw Exception(
        localizedStrings['google_login_failed']?.replaceAll('{error}', loginResult['error']) ??
            'Đăng nhập Google thất bại: ${loginResult['error']}',
      );
    } catch (e) {
      print('Google login error: $e');
      throw Exception(
        localizedStrings['connection_error']?.replaceAll('{error}', e.toString()) ??
            'Lỗi đăng nhập Google: $e',
      );
    }
  }

  Future<Map<String, dynamic>> _attemptGoogleLogin(
      String idToken,
      Map<String, String> localizedStrings,
      ) async {
    final url = Uri.parse('${ApiConfig.getFullUrl(ApiConfig.authGoogleLogin)}?idToken=$idToken');

    try {
      final response = await _client.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'accept': '*/*',
        },
      );

      print('=== GOOGLE LOGIN RESPONSE ===');
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final token = data['AccessToken']?.toString() ??
            data['accessToken']?.toString() ??
            data['token']?.toString();
        if (token == null) {
          throw Exception('Thiếu token trong phản hồi API');
        }

        final userId = _extractUserIdFromToken(token) ?? data['userId']?.toString();
        if (userId == null) {
          throw Exception('Không thể lấy userId từ token hoặc dữ liệu API');
        }

        _appStateNotifier?.resetState();
        await _userManager.saveUserToken(token);
        await _userManager.saveUserId(userId);
        final user = await _userService.getUserInfo(userId, token);
        await _userManager.saveUserInfo(userId, user);
        await _appStateNotifier?.fetchUserProfile(forceRefresh: true);

        return {
          'success': true,
          'data': {
            'accessToken': token,
            'userId': userId,
          },
        };
      } else {
        return {
          'success': false,
          'statusCode': response.statusCode,
          'error': localizedStrings['login_failed']
              ?.replaceAll('{statusCode}', response.statusCode.toString()) ??
              'Đăng nhập Google thất bại: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  Future<Map<String, dynamic>> _attemptGoogleRegistration(
      Map<String, dynamic> userInfo,
      Map<String, String> localizedStrings,
      ) async {
    final url = Uri.parse(ApiConfig.getFullUrl(ApiConfig.authRegister));
    final email = userInfo['email']?.toString() ?? '';
    final name = userInfo['name']?.toString() ?? 'Google User';
    final userName = email.split('@')[0];
    final defaultPassword = 'GoogleUser123!';

    final body = jsonEncode({
      'name': name,
      'email': email,
      'userName': userName,
      'password': defaultPassword,
      'verifyPassword': defaultPassword,
      'phoneNumber': '0000000000',
      'dateOfBirth': '1990-01-01T00:00:00.000Z',
      'gender': 1,
      'userImage': userInfo['userImage']?.toString(),
      'isGoogleUser': true,
    });

    try {
      final response = await _client.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'accept': '*/*',
        },
        body: body,
      );

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': localizedStrings['google_register_success'] ??
              'Tài khoản Google đã được tạo thành công',
        };
      }

      throw Exception(
        localizedStrings['google_register_failed']?.replaceAll('{statusCode}', response.statusCode.toString()) ??
            'Đăng ký Google thất bại: ${response.statusCode}',
      );
    } catch (e) {
      print('Google registration error: $e');
      return {
        'success': false,
        'message': e.toString(),
      };
    }
  }

  Future<Map<String, dynamic>> _createFallbackGoogleLogin(
      GoogleSignInAccount googleUser,
      Map<String, String> localizedStrings,
      ) async {
    try {
      final tempUserId = _generateTempUserId(googleUser.email);
      final mockToken = 'google_temp_${DateTime.now().millisecondsSinceEpoch}';

      _appStateNotifier?.resetState();
      await _userManager.saveUserToken(mockToken);
      await _userManager.saveUserId(tempUserId);
      final tempUserInfo = {
        'id': tempUserId,
        'name': googleUser.displayName ?? 'Google User',
        'email': googleUser.email,
        'userImage': googleUser.photoUrl ?? 'assets/images/profile.jpg',
        'donationCount': 0,
        'bloodType': null,
        'job': null,
        'dateOfBirth': null,
        'address': null,
        'passportNumber': null,
        'latitude': null,
        'longitude': null,
        'gender': null,
        'phoneNumber': null,
      };

      await _userManager.saveUserInfo(tempUserId, tempUserInfo);

      return {
        'accessToken': mockToken,
        'userId': tempUserId,
      };
    } catch (e) {
      throw Exception(
        localizedStrings['fallback_login_failed'] ?? 'Không thể tạo phiên đăng nhập tạm thời: $e',
      );
    }
  }

  String? _extractUserIdFromToken(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) {
        print('Invalid JWT: Token does not have 3 parts');
        return null;
      }

      final payload = parts[1];
      final normalizedPayload = payload.padRight(payload.length + (4 - payload.length % 4) % 4, '=');
      final decodedPayload = utf8.decode(base64Url.decode(normalizedPayload));
      final payloadMap = jsonDecode(decodedPayload) as Map<String, dynamic>;

      final userId = payloadMap['userId']?.toString();
      print('Extracted userId from token: $userId');
      return userId;
    } catch (e) {
      print('Error extracting userId from token: $e');
      return null;
    }
  }

  bool _isValidGoogleUserInfo(Map<String, dynamic> userInfo) {
    return userInfo['email'] != null &&
        userInfo['email'].toString().isNotEmpty &&
        userInfo['name'] != null &&
        userInfo['name'].toString().isNotEmpty;
  }

  String _generateTempUserId(String email) {
    final emailHash = email.hashCode.abs().toString();
    return 'google_temp_$emailHash';
  }

  // Kiểm tra thông tin email/phone đã tồn tại chưa
  Future<Map<String, dynamic>> checkInformation({
    required String email,
    required String phoneNumber,
  }) async {
    try {
      final url = Uri.parse('${ApiConfig.getFullUrl('/api/auth/check-information')}');

      final response = await _client.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'accept': '*/*',
        },
        body: jsonEncode({
          'email': email,
          'phoneNumber': phoneNumber,
        }),
      );

      print('=== CHECK INFORMATION RESPONSE ===');
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'data': data,
        };
      } else {
        final errorData = jsonDecode(response.body);
        return {
          'success': false,
          'error': errorData['message'] ?? 'Kiểm tra thông tin thất bại',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Lỗi kết nối: ${e.toString()}',
      };
    }
  }

  // Tạo tài khoản mới
  Future<Map<String, dynamic>> createAccount({
    required RegistrationModel registrationData,
    Map<String, String>? localizedStrings,
  }) async {
    try {
      final url = Uri.parse(ApiConfig.getFullUrl(ApiConfig.authCreateAccount));

      final response = await _client.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'accept': '*/*',
        },
        body: jsonEncode(registrationData.toJson()),
      );

      print('=== CREATE ACCOUNT RESPONSE ===');
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = jsonDecode(response.body);

        // Lấy token từ response
        final token = data['AccessToken']?.toString() ??
            data['accessToken']?.toString() ??
            data['token']?.toString();

        if (token == null) {
          throw Exception('Thiếu token trong phản hồi API');
        }

        final userId = _extractUserIdFromToken(token) ?? data['userId']?.toString();
        if (userId == null) {
          throw Exception('Không thể lấy userId từ token hoặc dữ liệu API');
        }

        // Reset state và lưu thông tin user
        _appStateNotifier?.resetState();
        await _userManager.saveUserToken(token);
        await _userManager.saveUserId(userId);

        // Lấy thông tin user từ API
        final user = await _userService.getUserInfo(userId, token);
        await _userManager.saveUserInfo(userId, user);
        await _appStateNotifier?.fetchUserProfile(forceRefresh: true);

        return {
          'success': true,
          'accessToken': token,
          'userId': userId,
          'message': localizedStrings?['registration_success'] ?? 'Đăng ký thành công',
        };
      } else {
        final errorData = jsonDecode(response.body);
        return {
          'success': false,
          'error': errorData['message'] ??
                   localizedStrings?['registration_failed'] ??
                   'Đăng ký thất bại: ${response.statusCode}',
        };
      }
    } catch (e) {
      print('Create account error: $e');
      return {
        'success': false,
        'error': localizedStrings?['connection_error']?.replaceAll('{error}', e.toString()) ??
                 'Lỗi đăng ký: $e',
      };
    }
  }
}
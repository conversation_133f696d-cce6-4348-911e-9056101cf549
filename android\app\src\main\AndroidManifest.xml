<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- <PERSON><PERSON><PERSON><PERSON> cần thiết -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <!-- Firebase Auth permissions -->
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.SEND_SMS" />

    <application
        android:label="Máu Cộng"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <!-- Cấu hình Google Sign-In -->
        <meta-data
            android:name="com.google.android.gms.signin.client_id"
            android:value="229734390839-3n21tokpf4sg0l6v7kur91dk78djvr9p.apps.googleusercontent.com" />
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <!-- API Key cho Google Maps (hãy thay bằng API Key thực tế của bạn) -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="YOUR_GOOGLE_MAPS_API_KEY" />
    </application>

    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>

        <!-- Hỗ trợ mở ứng dụng lịch mặc định -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="content" />
            <!--            <data android:host="com.android.calendar" />-->
        </intent>

        <!-- Hỗ trợ mở Google Calendar -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https" />
            <!--            <data android:host="calendar.google.com" />-->
        </intent>

        <!-- Hỗ trợ mở Google Maps -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https" />
            <!--            <data android:host="www.google.com" />-->
        </intent>

        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="geo" />
        </intent>
        <package android:name="com.google.android.apps.maps" />

        <!-- Hỗ trợ mở trình duyệt -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.BROWSABLE" />
            <data android:scheme="https" />
        </intent>

    </queries>

</manifest>

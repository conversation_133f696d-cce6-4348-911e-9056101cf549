import 'package:bloodplus/core/language_helper/localization.dart';
import 'package:bloodplus/core/widgets/custom_button.dart';
import 'package:bloodplus/data/manager/app_state_notifier.dart';
import 'package:bloodplus/data/manager/user_manager.dart';
import 'package:bloodplus/data/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:bloodplus/core/constants/app_colors.dart';
import 'package:provider/provider.dart';
import 'forgot_password_screen.dart';
import 'check_information_screen.dart';
import '../home/<USER>';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  bool _obscureText = true;
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _isLoading = false;

  void _handleLogin() async {
    final localizations = AppLocalizations.of(context);
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(localizations.translate('please_fill_all_fields')),
          backgroundColor: AppColors.darkRed,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final appStateNotifier = Provider.of<AppStateNotifier>(context, listen: false);
      final authService = AuthService(appStateNotifier: appStateNotifier);
      final response = await authService.login(
        _emailController.text,
        _passwordController.text,
        {
          'login_success': localizations.translate('login_success'),
          'login_failed': localizations.translate('login_failed'),
          'connection_error': localizations.translate('connection_error'),
        },
      );

      // Lưu token vào SharedPreferences
      final userManager = UserManager();
      await userManager.saveUserToken(response['accessToken']);

      // Hiển thị thông báo đăng nhập thành công
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(localizations.translate('login_successful')),
          backgroundColor: AppColors.lowerRed,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          duration: const Duration(seconds: 2),
        ),
      );

      // Chuyển đến HomeScreen
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const HomeScreen(),
            ),
          );
        }
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Đăng nhập thất bại: $e'),
          backgroundColor: AppColors.darkRed,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _handleGoogleLogin() async {
    final localizations = AppLocalizations.of(context);
    setState(() => _isLoading = true);

    try {
      final appStateNotifier = Provider.of<AppStateNotifier>(context, listen: false);
      final authService = AuthService(appStateNotifier: appStateNotifier);

      final response = await authService.loginWithGoogle({
        'login_success': localizations.translate('login_success'),
        'login_failed': localizations.translate('login_failed'),
        'connection_error': localizations.translate('connection_error'),
      });

      if (response.containsKey('error')) {
        if (response['error'] == 'Đăng nhập Google bị hủy') {
          return;
        }
        throw Exception(response['error']);
      }

      if (!response.containsKey('accessToken') || response['accessToken'] == null) {
        throw Exception('Không nhận được token từ server');
      }

      final userManager = UserManager();
      await userManager.saveUserToken(response['accessToken']);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(localizations.translate('login_successful')),
            backgroundColor: AppColors.lowerRed,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            duration: const Duration(seconds: 2),
          ),
        );

        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => const HomeScreen()),
            );
          }
        });
      }
    } catch (e) {
      // Get user-friendly error message
      String errorMessage = _getGoogleLoginErrorMessage(e.toString());

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: AppColors.darkRed,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Get user-friendly error message for Google login errors
  String _getGoogleLoginErrorMessage(String error) {
    final errorLower = error.toLowerCase();

    if (errorLower.contains('tài khoản chưa tồn tại') ||
        errorLower.contains('không thể tạo tài khoản tự động')) {
      return 'Đang tự động tạo tài khoản Google cho bạn...';
    } else if (errorLower.contains('đăng ký thành công nhưng đăng nhập lại thất bại')) {
      return 'Tài khoản đã được tạo thành công. Vui lòng thử đăng nhập lại.';
    } else if (errorLower.contains('lỗi kết nối') || errorLower.contains('connection')) {
      return 'Lỗi kết nối mạng. Vui lòng kiểm tra internet và thử lại.';
    } else if (errorLower.contains('không nhận được token')) {
      return 'Lỗi xác thực từ server. Vui lòng thử lại.';
    } else if (errorLower.contains('không thể tạo phiên đăng nhập tạm thời')) {
      return 'Lỗi hệ thống. Vui lòng thử lại sau.';
    } else {
      return 'Đăng nhập Google thất bại. Vui lòng thử lại sau.';
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  localizations.translate('login'),
                  style: const TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: AppColors.black,
                  ),
                ),
                const SizedBox(height: 40),
                TextField(
                  controller: _emailController,
                  keyboardType: TextInputType.emailAddress,
                  decoration: InputDecoration(
                    hintText: localizations.translate('enter_your_email'),
                    prefixIcon: const Icon(Icons.email_outlined),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.grey[200],
                  ),
                ),
                const SizedBox(height: 20),
                TextField(
                  controller: _passwordController,
                  obscureText: _obscureText,
                  decoration: InputDecoration(
                    hintText: localizations.translate('enter_your_password'),
                    prefixIcon: const Icon(Icons.lock_outline),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscureText ? Icons.visibility_off : Icons.visibility,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscureText = !_obscureText;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.grey[200],
                  ),
                ),
                const SizedBox(height: 10),
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ForgotPasswordScreen(),
                        ),
                      );
                    },
                    child: Text(
                      localizations.translate('forgot_password'),
                      style: const TextStyle(
                        color: Colors.blue,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                SizedBox(
                  width: double.infinity,
                  child: CustomButton(
                    text: _isLoading
                        ? 'Đang đăng nhập...'
                        : localizations.translate('login'),
                    color: AppColors.primaryRed,
                    onPressed: _isLoading ? null : () => _handleLogin(),
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    borderRadius: 10,
                  ),
                ),
                const SizedBox(height: 15),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      localizations.translate('dont_have_account'),
                      style: const TextStyle(fontSize: 15, color: AppColors.black),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const CheckInformationScreen(),
                          ),
                        );
                      },
                      child: Text(
                        localizations.translate('sign_up'),
                        style: const TextStyle(
                          fontSize: 17,
                          color: AppColors.primaryRed,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Text(
                  localizations.translate('or_login_with'),
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 20),
                OutlinedButton.icon(
                  onPressed: _isLoading ? null : _handleGoogleLogin,
                  icon: Image.asset(
                    'assets/icons/google_logo.png',
                    width: 24,
                    height: 24,
                  ),
                  label: Text(
                    localizations.translate('sign_in_with_google'),
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColors.black,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    minimumSize: const Size(double.infinity, 50),
                    side: const BorderSide(color: Colors.grey),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                OutlinedButton.icon(
                  onPressed: () {
                    // Apple sign in functionality to be implemented
                  },
                  icon: const Icon(
                    Icons.apple,
                    color: AppColors.black,
                    size: 24,
                  ),
                  label: const Text(
                    'Sign in with Apple',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppColors.black,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    minimumSize: const Size(double.infinity, 50),
                    side: const BorderSide(color: Colors.grey),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                OutlinedButton.icon(
                  onPressed: () {
                    // Facebook sign in functionality to be implemented
                  },
                  icon: Image.asset(
                    'assets/icons/facebook_logo.png',
                    width: 24,
                    height: 24,
                  ),
                  label: const Text(
                    'Sign in with Facebook',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppColors.black,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    minimumSize: const Size(double.infinity, 50),
                    side: const BorderSide(color: Colors.grey),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:bloodplus/core/constants/app_colors.dart';
import 'package:bloodplus/core/widgets/custom_button.dart';
import 'package:bloodplus/data/services/auth_service.dart';
import 'package:bloodplus/data/services/phone_auth_service.dart';
import 'package:bloodplus/data/manager/app_state_notifier.dart';
import 'package:bloodplus/presentation/features/auth/phone_verification_screen.dart';
import 'package:provider/provider.dart';

class CheckInformationScreen extends StatefulWidget {
  const CheckInformationScreen({Key? key}) : super(key: key);

  @override
  _CheckInformationScreenState createState() => _CheckInformationScreenState();
}

class _CheckInformationScreenState extends State<CheckInformationScreen> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  late final AuthService _authService;
  final PhoneAuthService _phoneAuthService = PhoneAuthService();
  
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final appStateNotifier = Provider.of<AppStateNotifier>(context, listen: false);
    _authService = AuthService(appStateNotifier: appStateNotifier);
  }

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _checkInformation() async {
    if (_emailController.text.isEmpty || _phoneController.text.isEmpty) {
      setState(() {
        _errorMessage = 'Vui lòng nhập đầy đủ email và số điện thoại';
      });
      return;
    }

    // Validate email format
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(_emailController.text)) {
      setState(() {
        _errorMessage = 'Email không hợp lệ';
      });
      return;
    }

    // Validate phone format
    if (!RegExp(r'^[0-9]{10,11}$').hasMatch(_phoneController.text)) {
      setState(() {
        _errorMessage = 'Số điện thoại không hợp lệ';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Gửi OTP trực tiếp (backend sẽ kiểm tra trùng lặp khi verify)
      await _sendOTP();
    } catch (e) {
      setState(() {
        _errorMessage = 'Lỗi gửi OTP: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _sendOTP() async {
    try {
      final result = await _phoneAuthService.sendOTP(
        phoneNumber: _phoneController.text,
        onCodeSent: (message) {
          if (mounted) {
            // Chuyển đến màn hình xác thực OTP
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PhoneVerificationScreen(
                  phoneNumber: _phoneController.text,
                  email: _emailController.text,
                ),
              ),
            );
          }
        },
        onError: (error) {
          if (mounted) {
            setState(() {
              _errorMessage = error;
            });
          }
        },
        onVerificationCompleted: (credential) {
          // Auto verification completed
          if (mounted) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PhoneVerificationScreen(
                  phoneNumber: _phoneController.text,
                  email: _emailController.text,
                ),
              ),
            );
          }
        },
      );

      if (result['success'] != true) {
        setState(() {
          _errorMessage = result['error'] ?? 'Gửi OTP thất bại';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Lỗi gửi OTP: ${e.toString()}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Kiểm tra thông tin',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              
              // Title
              const Text(
                'Nhập thông tin để đăng ký',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),

              const SizedBox(height: 8),

              const Text(
                'Chúng tôi sẽ gửi mã OTP để xác thực số điện thoại của bạn',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              
              const SizedBox(height: 40),
              
              // Email field
              const Text(
                'Email',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _emailController,
                keyboardType: TextInputType.emailAddress,
                decoration: InputDecoration(
                  hintText: 'Nhập email của bạn',
                  prefixIcon: const Icon(Icons.email_outlined),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.grey[100],
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Phone field
              const Text(
                'Số điện thoại',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _phoneController,
                keyboardType: TextInputType.phone,
                decoration: InputDecoration(
                  hintText: 'Nhập số điện thoại',
                  prefixIcon: const Icon(Icons.phone),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.grey[100],
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Error message
              if (_errorMessage != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(
                      color: Colors.red,
                      fontSize: 14,
                    ),
                  ),
                ),
              
              const SizedBox(height: 40),
              
              // Continue Button
              SizedBox(
                width: double.infinity,
                child: CustomButton(
                  text: _isLoading ? 'Đang kiểm tra...' : 'Tiếp tục',
                  color: AppColors.primaryRed,
                  onPressed: _isLoading ? null : _checkInformation,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  borderRadius: 10,
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Info text
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.blue,
                      size: 20,
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Chúng tôi sẽ gửi mã xác thực đến số điện thoại của bạn để xác nhận danh tính.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }
}
